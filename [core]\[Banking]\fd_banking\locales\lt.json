{"ui": {"currency_language": "en-Us", "calendar_language": "lt", "currency": "USD", "personal": "Asmeninė", "business": "<PERSON>ers<PERSON>", "shared": "<PERSON><PERSON><PERSON>", "unknown": "Nežinoma", "deposit_action": "Įmokėti", "withdraw_action": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transferin_action": "<PERSON><PERSON><PERSON> į", "transferout_action": "<PERSON><PERSON><PERSON>", "payment_action": "Mokė<PERSON><PERSON>", "other_unknown": "<PERSON><PERSON>", "logout": "<PERSON>si<PERSON><PERSON><PERSON>", "total_account_balance": "Galutinis sąskaitos likutis", "accounts": "Sąskaitos", "account": "Sąskaita", "type": "<PERSON><PERSON><PERSON><PERSON>", "iban": "IBAN", "balance": "<PERSON><PERSON><PERSON>", "if_you_havent_please_select_account_on_the_left": "<PERSON><PERSON><PERSON>, prašome pasirinkti sąskaitą kairėje pu<PERSON>je", "transaction_history": "Operacijų istorija", "deposit": "Įnėšimas", "withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transfer": "<PERSON><PERSON><PERSON>", "members": "<PERSON><PERSON><PERSON>", "export_transactions": "Eksportuoti Operacijas", "delete_account": "<PERSON><PERSON><PERSON><PERSON>", "made_by": "Sukurta", "message": "Žinute", "no_more_records": "Įrašų nebėra", "error_occurred": "<PERSON><PERSON> k<PERSON>. prašome pabandyti iš naujo.", "search_label": "Ieškoti", "adding_member_failed_please_try_again": "Prid<PERSON><PERSON> narį ne<PERSON>, prašome bandyti iš naujo.", "citizen_id": "<PERSON><PERSON><PERSON> kodas", "can_deposit_label": "Gali įmokėti", "can_deposit_description": "<PERSON><PERSON> <PERSON><PERSON> gal<PERSON> įmokėti iš š<PERSON> s<PERSON>ska<PERSON>?", "can_withdraw_label": "<PERSON><PERSON>", "can_withdraw_description": "<PERSON><PERSON> <PERSON><PERSON> gal<PERSON> iš<PERSON>imti iš š<PERSON> sąskaitos?", "can_transfer_label": "<PERSON><PERSON>", "can_transfer_description": "<PERSON><PERSON> <PERSON><PERSON> gal<PERSON> pervesti iš šios sąskaitos?", "can_export_label": "Gali eksportuoti", "can_export_description": "<PERSON><PERSON> <PERSON><PERSON> galės eksportuoti mokėjimų istoriją iš <PERSON> s<PERSON>skaito<PERSON>?", "can_can_control_label": "<PERSON><PERSON> valdyti narius", "can_can_control_description": "<PERSON><PERSON> <PERSON><PERSON> gal<PERSON>s valdyti kitus šios sąskaitos narius?", "add_label": "<PERSON><PERSON><PERSON><PERSON>", "only_letters_and_numbers_allowed": "<PERSON><PERSON><PERSON><PERSON><PERSON> tik <PERSON>, skaičiai bei tarpai.", "citizen_id_must_be_a_string": "Asmens kodas privalo buti sudarytas iš simbolių.", "citizen_id_is_required": "<PERSON><PERSON><PERSON> kodas yra privalomas.", "citizen_id_must_be_at_least_8_characters_long": "<PERSON><PERSON><PERSON> kodas pri<PERSON>o b<PERSON> bent 8-ų simbolių ilgio.", "citizen_id_must_be_at_most_8_characters_long": "As<PERSON>s kodas privalo būti ne daugiau kaip 8-ų simbolių ilgio.", "name_must_be_a_string": "Pavadinimas privalo buti sudarytas iš simbolių", "name_is_required": "Pavadinimas yra privalomas.", "minimum_name_length_5": "Minimalus pavadinimo ilgis yra 5-i simboliai.", "maximum_name_length_50": "<PERSON><PERSON><PERSON><PERSON> pavadinimo ilgis yra 50-t simbolių.", "creation_failed_please_try_again": "<PERSON><PERSON><PERSON><PERSON>, prašome bandyti dar kartą.", "create_account": "Sukurti paskyrą", "create_account_description": "Prašome įveskite paskyros pavadinimą apačioje.", "creation_name_label": "Pavadinimas", "creation_name_placeholder": "Atsitiktinis paskyr<PERSON> pavadin<PERSON>s", "creattion_button_label": "Sukurti paskyrą", "close_button_tooltip": "Uždaryti", "remove_member": "<PERSON><PERSON><PERSON><PERSON> narį", "remove_member_description": "Ar jūs tikrai norite pa<PERSON>lint<PERSON> šį narį? Šis veiksmas yra negrįžtamas ir jus negalėsite jo sugrąžinti. Prašome apačioje nurodyti asmens kodą:", "remove_member_input_label": "Sąskaitos numeris", "remove_member_confirm_removal_button": "Pat<PERSON><PERSON><PERSON> p<PERSON>", "remove_member_cancel_button": "At<PERSON>uk<PERSON> ve<PERSON>", "iban_must_be_a_string": "Sąskaitos numeris (IBAN) privalo buti sudarytas iš simbolių.", "iban_is_required": "Sąskaitos numeris (IBAN) yra privalomas.", "iban_do_not_match": "Įvesta sąskaita nesutampa", "delete_failed_please_try_again": "<PERSON><PERSON><PERSON><PERSON>, prašome bandyti iš naujo.", "delete_account_title": "<PERSON><PERSON><PERSON><PERSON>", "delete_account_description": "Ar jūs tikrai norite pašalinti šią sąskaitą? Šis veiksmas yra negrįžtamas ir jus negalėsite jo sugrąžinti. Prašome apačioje nurodyti sąskaitos numerį (IBAN):", "delete_account_input_label": "Sąskaitos numeris", "delete_account_in_account_currently_there_is": "šiuo metu sąskaitoje yra:", "delete_account_this_money_will_be_lost": "Šie pinigai bus automatiškai pervesti į miesto fondą, prie<PERSON> p<PERSON>šalinima, prašome pervesti juos į kitą sąskaitą.", "delete_account_confirm_removal_button": "Pat<PERSON><PERSON><PERSON> p<PERSON>", "delete_account_cancel_button": "At<PERSON>uk<PERSON> ve<PERSON>", "amount_must_be_a_number": "Suma turi būti n<PERSON><PERSON>.", "amount_is_required": "Suma yra privaloma.", "amount_must_be_greater_than_0": "Suma privalo b<PERSON>ti didesne nei $0.", "amount_must_be_less_than_1000000000": "Su<PERSON> privalo b<PERSON><PERSON> ma<PERSON> nei $1,000,000,000.0", "amount_must_be_integer": "Suma privalo b<PERSON>ti nurodyta sveikaisiais skaičiais.", "you_dont_have_enough_cash": "Sąskaitos likutis yra <PERSON>.", "description_must_be_a_string": "Aprašymas privalo buti sudarytas iš simbolių.", "description_is_required": "Aprašymas yra privaloma<PERSON>.", "description_must_be_at_least_3_characters_long": "<PERSON><PERSON>š<PERSON><PERSON> turi būti ne trumpesnis kaip 3-s simboliai.", "deposit_failed_please_try_again": "Įmoka ne<PERSON>, prašome bandyti iš naujo.", "deposit_title": "Įmoka", "deposit_description": "Žemiau prašome pasirinkite suma kuria jūs noėtumėte įmokėti į pasirinktą sąskaitą.", "deposit_in_description": "Indėlis:", "amount_label": "<PERSON><PERSON>", "in_pocket_you_have": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> metu jūs turite:", "description_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deposit_modal_description_placeholder": "Įmoka už naują automobilį", "deposit_modal_confirm_button": "Įmoką", "invalid_type_error": "Netinkamas tipas", "required_error": "<PERSON><PERSON> laukelis yra privalomas", "editing_member_failed_please_try_again": "<PERSON><PERSON> red<PERSON>, prašome bandyti iš naujo.", "edit_member_title": "Redaguoti narį", "edit_member_confirm_button": "Red<PERSON><PERSON><PERSON>", "start_date_must_be_a_date": "Pradinė data privalo buti nurodyta datos formatu.", "start_date_is_required": "Pradinė data yra privaloma.", "end_date_must_be_a_date": "Galutinė data privalo buti nurodyta datos formatu.", "end_date_is_required": "Galutinė data yra privaloma.", "exporting_failed_please_try_again": "Eksport<PERSON><PERSON>, prašome bandyti iš naujo.", "export_title": "Eksportavimų istorija", "export_description": "Apačioje jūs galite pasirinkti datas. Tarp pasirinktų datų, visi pervedimai bus eksportuoti CSV formatu.", "current_account": "<PERSON><PERSON><PERSON><PERSON> sąskaita:", "please_download_file": "Prašome atsisiųsti eksportuota CSV forma:", "export_here": "čia", "export_button": "Eksportavimų istorija", "members_management_title": "Narių valdymas", "members_management_description": "Apač<PERSON><PERSON> jūs galite matyti visus sąskaitos narius. <PERSON><PERSON><PERSON> gal<PERSON>, redaguoti ar p<PERSON><PERSON>lint<PERSON> juos.", "members_management_managing_currently": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> narį", "members_management_search_placeholder": "Pa<PERSON>š<PERSON>", "members_management_add_new_member_button": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> narį", "members_management_identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "members_management_can_deposit": "Gali įmokėti", "members_management_can_withdraw": "<PERSON><PERSON>", "members_management_can_transfer": "<PERSON><PERSON>", "members_management_can_export": "Gali eksportuoti", "members_management_can_control": "<PERSON><PERSON> valdyti narius", "yes_label": "<PERSON><PERSON>", "no_label": "Ne", "edit_label": "Red<PERSON><PERSON><PERSON>", "account_number_invalid_type": "Sąskaitos numeris (IBAN) privalo buti sudarytas iš s<PERSON> ", "account_number_is_required": "Sąskaitos numeris (IBAN) yra privalomas", "player_id_invalid_type": "Žaidėjo ID privalo buti sudarytas iš s<PERSON>ų", "player_id_is_required": "Žaidėjo ID yra privalomas", "there_is_not_enough_money_in_account": "Nepakankamas pinigų likutis sąskaitoje.", "account_number_or_player_id_is_required": "Sąskaitos numeris (IBAN) arba žaidėjo ID yra privalomas", "fill_in_only_one_field_account_number_or_player_id": "Užpildykite viena laukelį, sąskaitos numeris (IBAN) arba žaidėjo ID.", "transfer_failed_please_try_again": "<PERSON><PERSON><PERSON><PERSON>, prašome bandyti iš naujo.", "transfer_title": "Pervedimas", "transfer_description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, prašome nurodykite kitą sąskaitos numerį arba žaidėjo ID kodą ir sumą kuria norėtumėte pervesti. Po duomenų įvedimo, pra<PERSON><PERSON> isiti<PERSON>, kad pervedimo sąskaita nurodyta teisingai, nes pinigai gali nukeliauti ne ten.", "account_number_label": "Sąskaitos numeris (IBAN)", "or_label": "arba", "player_id_label": "Žaidėjo ID", "in_account_currently_there_is": "Sąskaitoje šiuo metu yra:", "no_funds": "Nėra l<PERSON>", "transfer_description_placeholder": "Štai tau truputelis pinigėliu.", "transfer_confirm_button": "<PERSON><PERSON><PERSON>", "withdraw_failed_please_try_again": "<PERSON><PERSON><PERSON><PERSON><PERSON>, prašome bandyti iš naujo.", "withdraw_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withdraw_description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, praš<PERSON> ivesti suma kuria jūs galite išsiimti į pasirinktą sąskaitą.", "withdraw_from_description": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>:", "withdraw_modal_description_placeholder": "<PERSON>ni<PERSON> pietums", "withdraw_modal_confirm_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deposited_to_account_title": "Inešti į sąskaitą", "withdrawn_from_account_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transfered_to_account_title": "<PERSON><PERSON><PERSON> į sąskaitą / Nuo: {0}", "transfered_from_account_title": "<PERSON><PERSON><PERSON> / <PERSON><PERSON>: {0}", "add_new_account": "Pridėti naują sąskaitą", "unknown_source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "atm": "ATM", "withdrawal_only": "Tiktai <PERSON>!", "frozen": "Užšaldyta", "unpaid": "<PERSON><PERSON>umok<PERSON><PERSON>", "paid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invoices": "Sąskaitos", "close": "Uždaryti", "no_unpaid_invoices": "Nėra nesumokėtu sąskaitų", "no_invoices_history": "Nėra sąskaitų istorijos", "no_due_date": "<PERSON><PERSON><PERSON> termino", "pay_all_invoices": "Apmokėti viską", "pay_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decline_button": "<PERSON><PERSON><PERSON>", "invoice_description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invoice_amount": "<PERSON><PERSON>", "invoice_issued_by": "<PERSON>šduota", "invoice_issued_on": "<PERSON>šduota", "invoice_due_date": "Terminas", "payment_for_invoice": "Mokėstis už <PERSON>skaitą / I<PERSON>: {0}", "status_paid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status_force_paid": "Priverstinai sumokėta", "status_declined_by_player": "Atmes<PERSON>", "invoice_status": "<PERSON><PERSON><PERSON><PERSON>", "invoice_unpaid": "<PERSON><PERSON>umok<PERSON><PERSON>", "invoice_paid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invoice_declined": "Atmes<PERSON>", "invoice_force_paid": "Priverstinai sumokėta", "invoice_cancelled": "<PERSON><PERSON><PERSON><PERSON>", "personal_account_title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "bank_app_title": "Bankas", "invoices_app_title": "Sąskaitos", "new_loan_title": "New loan", "new_loan_description": "Below, please enter the amount you want to loan. Everything will be automatically calculated based on your input.", "loan_type_label": "Loan type", "loan_amount_label": "Loan amount", "loan_payments_label": "Payments", "interest_rate_title": "Interest rate", "loan_duration_title": "Payments", "loan_each_payment_title": "Each payment", "interest_rate_amount_title": "Interest rate amount", "loan_total_amount_with_interest_title": "Total amount with interest", "day_single": "day", "day_plural": "days", "week_single": "week", "week_plural": "weeks", "request_modal_confirm_button": "Request Loan", "loan_select_type_placeholder": "Select loan type", "loans": "Loans", "request_new_loan": "Request new loan", "loan_amount_must_be_a_number": "Loan amount must be a number", "loan_amount_is_required": "Loan amount is required", "loan_amount_is_too_low": "Loan amount is too low", "loan_amount_is_too_high": "Loan amount is too high", "loan_duration_is_required": "Loan duration is required", "loan_duration_must_be_a_number": "Loan duration must be a number", "loan_duration_is_too_low": "Loan duration is too low", "loan_duration_is_too_high": "Loan duration is too high", "loan_request_failed_please_try_again": "Loan request failed, please try again.", "manage_loans": "Manage loans", "no_loan_requests": "No loan requests", "management_requests_tab": "Requests", "management_active_tab": "Active", "management_paid_off_tab": "Paid off", "management_other_tab": "Other", "approve_loan": "Approve", "reject_loan": "Reject", "requested_loan_overview_title": "Requested loan overview", "loan_type": "Loan type", "loan_player_identifier": "Citizen ID", "loan_full_name": "Full name", "loan_player_credit_score": "Credit score", "loan_player_unpaid_loans": "Unpaid loans", "loan_player_paid_loans": "Paid loans", "loan_player_cancelled_loans": "Cancelled loans", "loan_approve_confirmation_title": "Approve loan", "confirm": "Confirm", "loan_approve_confirmation": "Are you sure you want to approve this loan?", "approval_confirmation_title": "Loan approval confirmation", "reject_confirmation_title": "Reject loan", "loan_reject_confirmation": "Are you sure you want to reject this loan?", "view": "View", "loan_paid_off": "Paid off", "no_loan_paid_off": "No paid off loans", "loans_pending_player_approval": "Pending player approval", "loans_pending_manager_approval": "Pending manager approval", "loans_active": "Active", "loans_cancelled": "Cancelled", "loans_rejected": "Rejected", "loans_unknown": "Unknown", "no_active_loans": "No active loans", "no_loan_other_loans": "No other loans", "pay": "Pay", "active": "Active", "paid_off": "Paid off", "pending_request": "Pending request", "other": "Other", "no_payments_yet": "No payments yet", "loan_already_paid_amount": "Paid", "loan_overview": "Loan overview", "payment_was_latest": "Payment was missed, but paid.", "payment_was_on_time": "Payment was on time.", "cancellation_confirmation_title": "Loan cancellation confirmation", "loan_cancellation_confirmation": "Are you sure you want to cancel this loan?", "cancel_loan": "Cancel loan"}, "not_provided": "Nesuteikta", "bank_legion_blip": "Bankas", "checking_documents": "Tik<PERSON>mi dokumentai...", "giving_back_documents": "Atiduodami dokumentai...", "checking_card": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>...", "giving_back_card": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>...", "open_bank_target": "Atidaryti banką", "open_bank_point": "Atidaryti banką", "bank_hawick_blip": "Bankas", "bank_del_perro_blip": "Bankas", "bank_route_68_blip": "Bankas", "bank_pacific_blip": "Bankas", "bank_paleto_blip": "Bankas", "open_atm_target": "Atidaryti ATM", "open_atm_point": "Atidaryti ATM", "system": "Sistema", "issue_personal_invoice_title": "Išrašyti asmeninę s<PERSON>skaitą", "issue_personal_invoice_description": "Tai bus asmeninis sąskaita ir pelnas keliaus tiesiai į jūsų asmeninę sąskaitą.", "issue_society_invoice_title": "Išrašyti darbo sąskaitą", "issue_society_invoice_description": "Tai bus darbo sąskaita ir pelnas keliaus tiesiai į darbo sąskaitą.", "billing_menu": "Atsiskaitymų saraša<PERSON>", "issue_society_lookup_title": "Asmens informacija", "issue_society_lookup_description": "<PERSON>ūs galite matyti kiek nesumokėtu sąskaitų turi asmuo.", "issue_invoice_dialog_title": "Išduoti sąskaitą", "player_id_title": "Žaidėjo ID", "invoice_amount": "Sąskaitos suma", "invoice_description": "Mokėsčio priežastis", "invalid_amount": "<PERSON><PERSON><PERSON> suma", "invalid_player_id": "<PERSON><PERSON><PERSON><PERSON>", "invalid_description": "Klaidinga mokėsčio priežastis", "player_not_online": "Žaidėjas nėra prisijungęs", "player_is_too_far": "Žadėjas yra per toli", "no_personal_account": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> nerasta", "invoice_issued": "Mokėjimas išduotas sėkmingai! ID: #%s", "not_in_society": "<PERSON><PERSON><PERSON>", "society_invoices_disabled": "Darbo sąskaitos yra <PERSON>", "no_business_account": "<PERSON><PERSON><PERSON> ne<PERSON>", "payment_for_invoice": "Mokėstis sąskaitai #%s, apraš<PERSON>as: %s", "invoice_payed": "Tavo išduota sąskaita buvo apmokėta. ID #%s, aprašymas: %s", "invoice_declined": "Tavo išduota sąskaitą buvo atmesta. ID #%s, aprašymas: %s", "cancel_issued_invoice_title": "Atšaukti sąskaitą", "cancel_issued_invoice_description": "Atmesti išduota sąskaitą, praš<PERSON> atkreipti dėmėsį, kad atmesti sąskaitos mokėsti gali asmuo išdavę<PERSON> jį.", "lookup_issued_invoice_title": "Surasti sąskaitą", "lookup_issued_invoice_description": "Jus galite išieškoti mokėščių informacijos", "lookup_citizen_dialog_title": "Surasti žaidėjo informaciją", "lookup_citizen_show_title": "Asmens informacija", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unpaid_invoices_count": "Neapmokėtos <PERSON>", "unpaid_invoices_sum": "Bendra neapmokėtu sąskaitų suma", "lookup_invoice_dialog_title": "Išieškoti mokėščių informacija", "invoice_id_title": "Sąskaitos ID", "invalid_invoice_id": "Klaidingas Sąskaitos ID", "lookup_invoice_show_title": "Sąskaitų išiekojimo informacija", "recipient": "<PERSON><PERSON><PERSON><PERSON>", "invoice_id": "Sąskaitos ID", "invoice_issued_by": "Sąskaitos Išdavėjas", "invoice_paid_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invoice_declined_status": "Atmes<PERSON>", "invoice_force_paid_status": "Priverstinai sumokėta", "invoice_cancelled_status": "<PERSON><PERSON><PERSON><PERSON>", "invoice_unpaid_status": "<PERSON><PERSON>umok<PERSON><PERSON>", "invoice_cancelled": "Mokėjimas atšauktas sėkmingai.", "overdue_invoices_processed": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> (%s) apdirbtas sėkmingai.", "account_not_found": "Sąskaitą nerasta", "account_frozen": "Sąskaita užšaldyta", "account_frozen_successfully": "Sąskaita užšaldyta sėkmingai.", "account_not_frozen": "Sąskaita neužšaldyta", "account_unfrozen_successfully": "Sąskaita atšaldyta sėkmingai.", "you_dont_have_permission_to_use_this_command": "<PERSON>ūs neturite privilegiju naudoti šią komandą.", "player_already_tracked": "Ž<PERSON><PERSON>jas jau yra sekamas", "player_is_not_being_tracked": "Žaidėjas nėra sekamas", "player_is_being_tracked": "<PERSON><PERSON><PERSON><PERSON> (%s) dabar yra sekamas", "player_tracking_removed": "<PERSON><PERSON><PERSON><PERSON> (%s) sekimas buvo panaikintas", "no_invoices_to_pay": "Nėra jokių neapmokėtu sąskaitų", "not_enough_money": "<PERSON>ūs neturite pakankamai pinigų", "invoices_payed": "Sąskaitos buvo sėkmingai apmokėtos. Visa suma: %s", "givecash_help_tip": "Suteikia grynųjų iš jūsų kišenių į pateiktą ID.", "givecash_help_param": "Tikslinio žaidėjo serverio ID.", "givecash_help_param2": "<PERSON><PERSON>, k<PERSON><PERSON> norite duoti.", "givecash_no_user": "Netinkamas t<PERSON>.", "givecash_no_amount": "Turite įvesti sumą, kurią norite duoti.", "givecash_no_zero": "Turite įvesti sumą, didesnę nei 0.", "givecash_no_nearby": "Netoliese nėra žaidėjų.", "givecash_give_money": "Tu davei $%s.", "givecash_receive_money": "Tu gavai $%s."}