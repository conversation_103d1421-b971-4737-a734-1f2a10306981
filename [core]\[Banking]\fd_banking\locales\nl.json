{"ui": {"currency_language": "nl-BE", "calendar_language": "nl", "currency": "EUR", "personal": "Persoonlijk", "business": "<PERSON><PERSON><PERSON><PERSON>", "shared": "<PERSON><PERSON><PERSON>", "unknown": "Onbekend", "deposit_action": "<PERSON><PERSON><PERSON>", "withdraw_action": "<PERSON><PERSON><PERSON><PERSON>", "transferin_action": "Stort in", "transferout_action": "Stort uit", "payment_action": "<PERSON><PERSON>", "other_unknown": "Ander", "logout": "Afmelden", "total_account_balance": "Totaal rekeningsaldo", "accounts": "Re<PERSON><PERSON>", "account": "Rekening", "type": "Type", "iban": "IBAN", "balance": "Balans", "if_you_havent_please_select_account_on_the_left": "<PERSON><PERSON> je dat nog niet hebt gedaan, selecteer dan links een account.", "transaction_history": "Transactiegeschiedenis", "deposit": "Stort", "withdraw": "Haal af", "transfer": "Transfer", "members": "<PERSON><PERSON>", "export_transactions": "Uitvoertransacties", "delete_account": "Verwijder Rekening", "made_by": "Gemaakt door", "message": "Bericht", "no_more_records": "<PERSON><PERSON> records meer", "error_occurred": "Er is een fout opgetreden, probeer het opnieuw.", "search_label": "<PERSON><PERSON>", "adding_member_failed_please_try_again": "<PERSON><PERSON> <PERSON> is mislukt, probeer het opnieuw.", "citizen_id": "Burger ID", "can_deposit_label": "Kan storten", "can_deposit_description": "Zal de burger op deze rekening kunnen storten?", "can_withdraw_label": "Kan zich terugtrekken", "can_withdraw_description": "Zal de burger kunnen opnemen van deze rekening?", "can_transfer_label": "Kan overdragen", "can_transfer_description": "Zal de burger van deze rekening kunnen overschrijven?", "can_export_label": "<PERSON>n u<PERSON>n", "can_export_description": "Kan de burger de transactiegeschiedenis exporteren?", "can_can_control_label": "Kan leden controleren", "can_can_control_description": "Zal de burger andere leden kunnen controleren?", "add_label": "Voeg toe", "only_letters_and_numbers_allowed": "Alleen letters, cijfers en spaties zijn toeges<PERSON>an.", "citizen_id_must_be_a_string": "Citizen ID moet een string zijn.", "citizen_id_is_required": "Een Citizen ID is vereist.", "citizen_id_must_be_at_least_8_characters_long": "Citizen ID moet ten minste 8 tekens lang zijn.", "citizen_id_must_be_at_most_8_characters_long": "Citizen ID mag maximaal 8 tekens lang zijn.", "name_must_be_a_string": "De naam moet een string zijn.", "name_is_required": "<PERSON>am is verplicht.", "minimum_name_length_5": "<PERSON> <PERSON><PERSON><PERSON><PERSON> van de <PERSON> is 5 tekens.", "maximum_name_length_50": "De maximale lengte van de <PERSON> is 50 tekens.", "creation_failed_please_try_again": "<PERSON><PERSON><PERSON><PERSON> mislukt, probeer opnieuw.", "create_account": "Account aan<PERSON>ken", "create_account_description": "<PERSON><PERSON> hieronder de naam in voor uw nieuwe account.", "creation_name_label": "<PERSON><PERSON>", "creation_name_placeholder": "Willekeurige account<PERSON>am", "creattion_button_label": "Account aan<PERSON>ken", "close_button_tooltip": "Sluit", "remove_member": "Verwijder lid", "remove_member_description": "Weet je zeker dat je dit lid wilt verwijderen? Deze actie is onomkeerbaar en u zult het niet kunnen herstellen. Hieronder, vul burger id nummer (IBAN) in dat is:", "remove_member_input_label": "Rekeningnummer", "remove_member_confirm_removal_button": "Bevestig ver<PERSON>", "remove_member_cancel_button": "Deze actie annuleren", "iban_must_be_a_string": "Het rekeningnummer (IBAN) moet een string zijn.", "iban_is_required": "<PERSON><PERSON> re<PERSON> (IBAN) is vereist.", "iban_do_not_match": "Ingevoerde rekening komt niet overeen", "delete_failed_please_try_again": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is mislukt, probeer het opnieuw.", "delete_account_title": "Bevestiging verwijderen", "delete_account_description": "Weet u zeker dat u deze account wilt verwijderen? Deze actie is onomkeerbaar en u zult het niet kunnen herstellen. Vul hieronder het rekeningnummer (IBAN) in dat is:", "delete_account_input_label": "Rekeningnummer", "delete_account_in_account_currently_there_is": "Op rekening, <PERSON><PERSON> wel:", "delete_account_this_money_will_be_lost": "Dit geld wordt overgemaakt naar het stadsfonds, maak het over naar een andere rekening voordat u de rekening verwijdert.", "delete_account_confirm_removal_button": "Verwijdering bevestigen", "delete_account_cancel_button": "Deze actie annuleren", "amount_must_be_a_number": "Het bedrag moet een getal zijn.", "amount_is_required": "Bedrag is vereist.", "amount_must_be_greater_than_0": "Het bedrag moet groter zijn dan 0.", "amount_must_be_less_than_1000000000": "Het bedrag moet minder dan €*************,0 zijn.", "amount_must_be_integer": "Het bedrag moet een geheel getal zijn.", "you_dont_have_enough_cash": "Je hebt niet genoeg geld.", "description_must_be_a_string": "De beschrijving moet een string zijn.", "description_is_required": "Beschrijving is vereist.", "description_must_be_at_least_3_characters_long": "De beschrijving moet minstens 3 tekens lang zijn.", "deposit_failed_please_try_again": "Storting mislukt, probeer het opnieuw.", "deposit_title": "Storting", "deposit_description": "Vul hieronder het bedrag in dat u op de geselecteerde rekening kunt storten.", "deposit_in_description": "<PERSON><PERSON><PERSON> in:", "amount_label": "Bedrag", "in_pocket_you_have": "In zak, heb je momenteel:", "description_label": "Beschrijving", "deposit_modal_description_placeholder": "Borg voor mijn nieuwe auto", "deposit_modal_confirm_button": "Storting", "invalid_type_error": "Ongeldig type", "required_error": "Veld is verplicht", "editing_member_failed_please_try_again": "Bewerken lid mislukt, probeer opnieuw.", "edit_member_title": "Lid bewerken", "edit_member_confirm_button": "Bewerk", "start_date_must_be_a_date": "De begindatum moet een datum zijn.", "start_date_is_required": "Startdatum is vereist.", "end_date_must_be_a_date": "De einddatum moet een datum zijn.", "end_date_is_required": "Einddatum is vereist.", "exporting_failed_please_try_again": "Het exporteren is mislukt, probeer het opnieuw.", "export_title": "Exportgeschiedenis", "export_description": "Hieronder kunt u data kiezen en tussen die data worden alle transacties geëxporteerd naar CSV-bestand.", "current_account": "Lopende rekening:", "please_download_file": "Download geëxporteerde CSV van:", "export_here": "hier", "export_button": "Exportgeschiedenis", "members_management_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "members_management_description": "Hieronder ziet u alle leden van dit account. U kunt nieuwe leden toevoegen, bewerken of verwijderen.", "members_management_managing_currently": "<PERSON><PERSON><PERSON>n", "members_management_search_placeholder": "Zoek op", "members_management_add_new_member_button": "<PERSON><PERSON><PERSON>n", "members_management_identifier": "Identificatiecode", "members_management_can_deposit": "<PERSON><PERSON>", "members_management_can_withdraw": "Kan intrekken", "members_management_can_transfer": "Kan overbrengen", "members_management_can_export": "Kan exporteren", "members_management_can_control": "Kan leden controleren", "yes_label": "<PERSON>a", "no_label": "<PERSON><PERSON>", "edit_label": "Bewerk", "account_number_invalid_type": "Het rekeningnummer (IBAN) moet een nummer zijn.", "account_number_is_required": "Rekeningnummer (IBAN) is vereist", "player_id_invalid_type": "Geluksnummer moet een nummer zijn", "player_id_is_required": "Geluksnummer is vereist", "there_is_not_enough_money_in_account": "Er is niet genoeg geld op de rekening", "account_number_or_player_id_is_required": "Rekeningnummer (IBAN) of geluksnummer is vereist.", "fill_in_only_one_field_account_number_or_player_id": "Vul slechts één veld in, rekeningnummer (IBAN) of geluksnummer", "transfer_failed_please_try_again": "<PERSON><PERSON><PERSON> mislukt, probeer het opnieuw.", "transfer_title": "Transfer", "transfer_description": "Vul hieronder een ander rekeningnummer of geluksnummer en het bedrag dat u wilt overmaken. Na het invoeren, gelieve te bevestigen dat de rekening correct is, omdat het geld misschien ergens anders heen gaat.", "account_number_label": "Rekeningnummer (IBAN)", "or_label": "of", "player_id_label": "Geluksnummer", "in_account_currently_there_is": "Op rekening, <PERSON><PERSON> wel:", "no_funds": "<PERSON><PERSON>", "transfer_description_placeholder": "Hier is een beetje geld voor je.", "transfer_confirm_button": "Transfer", "withdraw_failed_please_try_again": "<PERSON><PERSON><PERSON><PERSON><PERSON> is mislukt, probeer het opnieuw.", "withdraw_title": "Intrekken", "withdraw_description": "Vul hieronder het bedrag in dat u kunt opnemen op de geselecteerde rekening.", "withdraw_from_description": "Terugtrekken uit:", "withdraw_modal_description_placeholder": "Lunchgeld", "withdraw_modal_confirm_button": "Intrekken", "deposited_to_account_title": "Storting op rekening", "withdrawn_from_account_title": "Op<PERSON> van <PERSON>", "transfered_to_account_title": "Overschrijving naar rekening / Van: {0}", "transfered_from_account_title": "Overschrijving van re<PERSON> / Naar: {0}", "add_new_account": "Nieuwe rekening toevoegen", "unknown_source": "<PERSON><PERSON><PERSON><PERSON> bron", "atm": "ATM", "withdrawal_only": "Alleen terugtrekken!", "frozen": "<PERSON><PERSON><PERSON><PERSON>", "unpaid": "Onbetaald", "paid": "<PERSON><PERSON>", "invoices": "<PERSON><PERSON><PERSON>", "close": "Sluit", "no_unpaid_invoices": "<PERSON><PERSON> on<PERSON> facturen", "no_invoices_history": "<PERSON><PERSON>", "no_due_date": "<PERSON><PERSON> verva<PERSON>", "pay_all_invoices": "Betaal alles:", "pay_button": "<PERSON><PERSON>", "decline_button": "Daling", "invoice_description": "Beschrijving", "invoice_amount": "Bedrag", "invoice_issued_by": "Uitgegeven door", "invoice_issued_on": "Uitgegeven op", "invoice_due_date": "Verschuldigd op", "payment_for_invoice": "Betaling voor factuur / Aan: {0}", "status_paid": "<PERSON><PERSON>", "status_force_paid": "<PERSON><PERSON><PERSON>", "status_declined_by_player": "Afgewezen", "invoice_status": "Status", "invoice_unpaid": "Onbetaald", "invoice_paid": "<PERSON><PERSON>", "invoice_declined": "Afgewezen", "invoice_force_paid": "<PERSON><PERSON><PERSON>", "invoice_cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "personal_account_title": "Primaire <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "bank_app_title": "Bank", "invoices_app_title": "<PERSON><PERSON><PERSON>", "new_loan_title": "New loan", "new_loan_description": "Below, please enter the amount you want to loan. Everything will be automatically calculated based on your input.", "loan_type_label": "Loan type", "loan_amount_label": "Loan amount", "loan_payments_label": "Payments", "interest_rate_title": "Interest rate", "loan_duration_title": "Payments", "loan_each_payment_title": "Each payment", "interest_rate_amount_title": "Interest rate amount", "loan_total_amount_with_interest_title": "Total amount with interest", "day_single": "day", "day_plural": "days", "week_single": "week", "week_plural": "weeks", "request_modal_confirm_button": "Request Loan", "loan_select_type_placeholder": "Select loan type", "loans": "Loans", "request_new_loan": "Request new loan", "loan_amount_must_be_a_number": "Loan amount must be a number", "loan_amount_is_required": "Loan amount is required", "loan_amount_is_too_low": "Loan amount is too low", "loan_amount_is_too_high": "Loan amount is too high", "loan_duration_is_required": "Loan duration is required", "loan_duration_must_be_a_number": "Loan duration must be a number", "loan_duration_is_too_low": "Loan duration is too low", "loan_duration_is_too_high": "Loan duration is too high", "loan_request_failed_please_try_again": "Loan request failed, please try again.", "manage_loans": "Manage loans", "no_loan_requests": "No loan requests", "management_requests_tab": "Requests", "management_active_tab": "Active", "management_paid_off_tab": "Paid off", "management_other_tab": "Other", "approve_loan": "Approve", "reject_loan": "Reject", "requested_loan_overview_title": "Requested loan overview", "loan_type": "Loan type", "loan_player_identifier": "Citizen ID", "loan_full_name": "Full name", "loan_player_credit_score": "Credit score", "loan_player_unpaid_loans": "Unpaid loans", "loan_player_paid_loans": "Paid loans", "loan_player_cancelled_loans": "Cancelled loans", "loan_approve_confirmation_title": "Approve loan", "confirm": "Confirm", "loan_approve_confirmation": "Are you sure you want to approve this loan?", "approval_confirmation_title": "Loan approval confirmation", "reject_confirmation_title": "Reject loan", "loan_reject_confirmation": "Are you sure you want to reject this loan?", "view": "View", "loan_paid_off": "Paid off", "no_loan_paid_off": "No paid off loans", "loans_pending_player_approval": "Pending player approval", "loans_pending_manager_approval": "Pending manager approval", "loans_active": "Active", "loans_cancelled": "Cancelled", "loans_rejected": "Rejected", "loans_unknown": "Unknown", "no_active_loans": "No active loans", "no_loan_other_loans": "No other loans", "pay": "Pay", "active": "Active", "paid_off": "Paid off", "pending_request": "Pending request", "other": "Other", "no_payments_yet": "No payments yet", "loan_already_paid_amount": "Paid", "loan_overview": "Loan overview", "payment_was_latest": "Payment was missed, but paid.", "payment_was_on_time": "Payment was on time.", "cancellation_confirmation_title": "Loan cancellation confirmation", "loan_cancellation_confirmation": "Are you sure you want to cancel this loan?", "cancel_loan": "Cancel loan"}, "not_provided": "<PERSON><PERSON> v<PERSON>", "bank_legion_blip": "Bank", "checking_documents": "Documenten controleren..", "giving_back_documents": "Documenten teruggeven..", "checking_card": "<PERSON><PERSON> be<PERSON>jken..", "giving_back_card": "<PERSON><PERSON><PERSON><PERSON> kaart..", "open_bank_target": "Open Bank", "open_bank_point": "Open Bank", "bank_hawick_blip": "Bank", "bank_del_perro_blip": "Bank", "bank_route_68_blip": "Bank", "bank_pacific_blip": "Bank", "bank_paleto_blip": "Bank", "open_atm_target": "Open ATM", "open_atm_point": "Open ATM", "system": "Systeem", "issue_personal_invoice_title": "Persoonlijke factuur opstellen", "issue_personal_invoice_description": "Dit is een persoonlijke factuur, en het geld gaat rechtstreeks naar uw persoonlijke rekening.", "issue_society_invoice_title": "<PERSON><PERSON><PERSON><PERSON><PERSON> van de maatschappelijke factuur", "issue_society_invoice_description": "Dit zal een maatschappelijke factuur zijn, en het geld zal rechtstreeks naar de maatschappelijke rekening gaan.", "billing_menu": "Factuur menu", "issue_society_lookup_title": "Burger opzoeken", "issue_society_lookup_description": "Je kunt zien hoeveel onbetaalde facturen de burger heeft.", "issue_invoice_dialog_title": "Factuur opstellen", "player_id_title": "Geluksnummer", "invoice_amount": "Factuurbedrag", "invoice_description": "Factuur reden", "invalid_amount": "Ongeldig bedrag", "invalid_player_id": "Ongeldige Geluksnummer", "invalid_description": "Ongeldige factuur reden", "player_not_online": "Burger is er niet", "player_is_too_far": "Burger is te ver", "no_personal_account": "Persoonlijke rekening niet gevonden", "invoice_issued": "Factuur succesvol uitgegeven! ID: #%s", "not_in_society": "Je zit niet in de maatschappij", "society_invoices_disabled": "<PERSON><PERSON><PERSON> van de maatschappij zijn uitgeschakeld", "no_business_account": "Zakelijke rekening niet gevonden", "payment_for_invoice": "Betaling voor factuur #%s, omschrijving: %s", "invoice_payed": "Uw factuur is betaald! ID #%s, omschrijving: %s", "invoice_declined": "Uw factuur is geweigerd! ID #%s, omschrijving: %s", "cancel_issued_invoice_title": "Factuur annuleren", "cancel_issued_invoice_description": "<PERSON>uleer uitgegeven factuur, let op: alleen de persoon die de factuur heeft uitgegeven kan deze annuleren.", "lookup_issued_invoice_title": "<PERSON><PERSON><PERSON>ur <PERSON>", "lookup_issued_invoice_description": "U kunt factuurinformatie opzoeken", "lookup_citizen_dialog_title": "burgerinfo opzoeken", "lookup_citizen_show_title": "Informati<PERSON> van <PERSON>urger", "identifier": "Identificatiecode", "unpaid_invoices_count": "Totaal onbetaalde facturen", "unpaid_invoices_sum": "Totaal bedrag aan onbetaalde facturen", "lookup_invoice_dialog_title": "Factuurinformatie opzoeken", "invoice_id_title": "Factuur ID", "invalid_invoice_id": "Ongeldige Factuur ID", "lookup_invoice_show_title": "Factuuropzoekinformatie", "recipient": "Ontvanger", "invoice_id": "Factuur ID", "invoice_issued_by": "Factuurverstrekker", "invoice_paid_status": "<PERSON><PERSON>", "invoice_declined_status": "Afgewezen", "invoice_force_paid_status": "<PERSON><PERSON><PERSON>", "invoice_cancelled_status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invoice_unpaid_status": "Onbetaald", "invoice_cancelled": "Factuur succesvol gean<PERSON>.", "overdue_invoices_processed": "Achterstallige facturen (%s) succesvol verwerkt.", "account_not_found": "Account niet gevonden", "account_frozen": "Rekening is bevroren", "account_frozen_successfully": "Account succesvol bevroren.", "account_not_frozen": "De rekening is niet bevroren", "account_unfrozen_successfully": "Account succesvol ontdooid.", "you_dont_have_permission_to_use_this_command": "Je hebt geen toestemming om dit commando te gebruiken.", "player_already_tracked": "Burger wordt al gevolgd", "player_is_not_being_tracked": "Burger wordt niet gevolgd", "player_is_being_tracked": "Burger (%s) wordt nu gevolgd", "player_tracking_removed": "Burger (%s) tracking verwijderd", "no_invoices_to_pay": "<PERSON>n facturen te betalen", "not_enough_money": "Je hebt niet genoeg geld", "invoices_payed": "Facturen succesvol betaald. Totaal bedrag: %s", "givecash_help_tip": "Geeft contant geld uit uw zakken aan het opgegeven identiteitsbewijs.", "givecash_help_param": "Server-<PERSON>.", "givecash_help_param2": "Bedrag dat u wilt geven.", "givecash_no_user": "Ongeldige doelspeler.", "givecash_no_amount": "U moet een bedrag invullen dat u wilt geven.", "givecash_no_zero": "U dient een bedrag boven de 0 in te vullen.", "givecash_no_nearby": "<PERSON>r zijn geen spelers in de buurt.", "givecash_give_money": "Jij gaf $%s.", "givecash_receive_money": "Jij ontving $%s."}