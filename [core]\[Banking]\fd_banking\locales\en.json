{"ui": {"currency_language": "en-US", "calendar_language": "en", "currency": "USD", "personal": "Personal", "business": "Business", "shared": "Shared", "unknown": "Unknown", "deposit_action": "<PERSON><PERSON><PERSON><PERSON>", "withdraw_action": "Withdraw", "transferin_action": "Transfer in", "transferout_action": "Transfer out", "payment_action": "Payment", "other_unknown": "Other", "logout": "Logout", "total_account_balance": "Total Account Balance", "accounts": "Accounts", "account": "Account", "type": "Type", "iban": "IBAN", "balance": "Balance", "if_you_havent_please_select_account_on_the_left": "If you haven't, please select account on the left.", "transaction_history": "Transaction History", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transfer": "Transfer", "members": "Members", "export_transactions": "Export Transactions", "delete_account": "Delete Account", "made_by": "Made by", "message": "Message", "no_more_records": "No more records", "error_occurred": "Error occurred, please try again.", "search_label": "Search", "adding_member_failed_please_try_again": "Adding member failed, please try again.", "citizen_id": "Citizen ID", "can_deposit_label": "Can deposit", "can_deposit_description": "Will player be able to deposit to this account?", "can_withdraw_label": "Can withdraw", "can_withdraw_description": "Will player be able to withdraw from this account?", "can_transfer_label": "Can transfer", "can_transfer_description": "Will player be able to transfer from this account?", "can_export_label": "Can export", "can_export_description": "Will player be able to export transaction history?", "can_can_control_label": "Can control members", "can_can_control_description": "Will player be able to control other members?", "add_label": "Add", "only_letters_and_numbers_allowed": "Only letters, numbers and spaces are allowed.", "citizen_id_must_be_a_string": "Citizen ID must be a string.", "citizen_id_is_required": "Citizen ID is required.", "citizen_id_must_be_at_least_8_characters_long": "Citizen ID must be at least 8 characters long.", "citizen_id_must_be_at_most_8_characters_long": "Citizen ID must be at most 8 characters long.", "name_must_be_a_string": "Name must be a string.", "name_is_required": "Name is required.", "minimum_name_length_5": "Minimum name length is 5 characters.", "maximum_name_length_50": "Maximum name length is 50 characters.", "creation_failed_please_try_again": "Creation failed, please try again.", "create_account": "Create Account", "create_account_description": "Below, please enter name for your new account.", "creation_name_label": "Name", "creation_name_placeholder": "Random account name", "creattion_button_label": "Create account", "close_button_tooltip": "Close", "remove_member": "Remove member", "remove_member_description": "Are you sure that you want to remove this member? This action is irreversible and you will not be able to recover it. Below, please enter citizen id number (iban) which is:", "remove_member_input_label": "Account number", "remove_member_confirm_removal_button": "Confirm removal", "remove_member_cancel_button": "Cancel this action", "iban_must_be_a_string": "Account number (IBAN) must be a string.", "iban_is_required": "Account number (IBAN) is required.", "iban_do_not_match": "Entered account does not match", "delete_failed_please_try_again": "Delete failed, please try again.", "delete_account_title": "Delete confirmation", "delete_account_description": "Are you sure that you want to delete this account? This action is irreversible and you will not be able to recover it. Below, please enter account number (iban) which is:", "delete_account_input_label": "Account number", "delete_account_in_account_currently_there_is": "In account, currenly there is:", "delete_account_this_money_will_be_lost": "This money will be transfered to city fund, please transfer it to other account before deleting account.", "delete_account_confirm_removal_button": "Confirm deletion", "delete_account_cancel_button": "Cancel this action", "amount_must_be_a_number": "Amount must be a number.", "amount_is_required": "Amount is required.", "amount_must_be_greater_than_0": "Amount must be greater than 0.", "amount_must_be_less_than_1000000000": "Amount must be less than $1,000,000,000.0", "amount_must_be_integer": "Amount must be integer.", "you_dont_have_enough_cash": "You don't have enough cash.", "description_must_be_a_string": "Description must be a string.", "description_is_required": "Description is required.", "description_must_be_at_least_3_characters_long": "Description must be at least 3 characters long.", "deposit_failed_please_try_again": "Depo<PERSON><PERSON> failed, please try again.", "deposit_title": "<PERSON><PERSON><PERSON><PERSON>", "deposit_description": "Below, please enter the amount you can to deposit into the selected account.", "deposit_in_description": "Depositing in:", "amount_label": "Amount", "in_pocket_you_have": "In pocket, you currently have:", "description_label": "Description", "deposit_modal_description_placeholder": "Deposit for my new car", "deposit_modal_confirm_button": "<PERSON><PERSON><PERSON><PERSON>", "invalid_type_error": "Invalid type", "required_error": "Field is required", "editing_member_failed_please_try_again": "Editing member failed, please try again.", "edit_member_title": "Edit member", "edit_member_confirm_button": "Edit", "start_date_must_be_a_date": "Start date must be a date.", "start_date_is_required": "Start date is required.", "end_date_must_be_a_date": "End date must be a date.", "end_date_is_required": "End date is required.", "exporting_failed_please_try_again": "Exporting failed, please try again.", "export_title": "Export history", "export_description": "Below, you can choose dates and in between those dates all of the transactions will be exported to CSV file.", "current_account": "Current account:", "please_download_file": "Please download exported CSV from:", "export_here": "here", "export_button": "Export history", "members_management_title": "Members management", "members_management_description": "Below, you can see all of the members of this account. You can add new members, edit or remove them.", "members_management_managing_currently": "Add new member", "members_management_search_placeholder": "Search", "members_management_add_new_member_button": "Add new member", "members_management_identifier": "Identifier", "members_management_can_deposit": "Can Deposit", "members_management_can_withdraw": "Can Withdraw", "members_management_can_transfer": "Can Transfer", "members_management_can_export": "Can Export", "members_management_can_control": "Can Control Members", "yes_label": "Yes", "no_label": "No", "edit_label": "Edit", "account_number_invalid_type": "Account number (IBAN) must be a number", "account_number_is_required": "Account number (IBAN) is required", "player_id_invalid_type": "Player ID must be a number", "player_id_is_required": "Player ID is required", "there_is_not_enough_money_in_account": "There is not enough money in account", "account_number_or_player_id_is_required": "Account number (IBAN) or Player ID is required", "fill_in_only_one_field_account_number_or_player_id": "Fill in only one field, account number (IBAN) or Player ID", "transfer_failed_please_try_again": "Transfer failed, please try again.", "transfer_title": "Transfer", "transfer_description": "Below, please enter other account number or player id and amount you want to transfer. After entering, please confirm and transfer account is correct, because money might go else where.", "account_number_label": "Account number (IBAN)", "or_label": "or", "player_id_label": "Player ID", "in_account_currently_there_is": "In account, currently there is:", "no_funds": "No funds", "transfer_description_placeholder": "Here is a little bit of money for you.", "transfer_confirm_button": "Transfer", "withdraw_failed_please_try_again": "Withdraw failed, please try again.", "withdraw_title": "Withdraw", "withdraw_description": "Below, please enter the amount you can to withdraw into the selected account.", "withdraw_from_description": "Withdrawing from:", "withdraw_modal_description_placeholder": "Lunch money", "withdraw_modal_confirm_button": "Withdraw", "deposited_to_account_title": "Deposit to account", "withdrawn_from_account_title": "<PERSON><PERSON><PERSON> from account", "transfered_to_account_title": "Transfer to account / From: {0}", "transfered_from_account_title": "Transfer from account / To: {0}", "add_new_account": "Add new account", "unknown_source": "Unknown source", "atm": "ATM", "withdrawal_only": "Withdrawal only!", "frozen": "Frozen", "unpaid": "Unpaid", "paid": "Paid", "invoices": "Invoices", "close": "Close", "no_unpaid_invoices": "No unpaid invoices", "no_invoices_history": "No invoices history", "no_due_date": "No due date", "pay_all_invoices": "Pay all:", "pay_button": "Pay", "decline_button": "Decline", "invoice_description": "Description", "invoice_amount": "Amount", "invoice_issued_by": "Issued by", "invoice_issued_on": "Issued on", "invoice_due_date": "Due on", "payment_for_invoice": "Payment for invoice / To: {0}", "status_paid": "Paid", "status_force_paid": "Force paid", "status_declined_by_player": "Declined", "invoice_status": "Status", "invoice_unpaid": "Unpaid", "invoice_paid": "Paid", "invoice_declined": "Declined", "invoice_force_paid": "Force paid", "invoice_cancelled": "Cancelled", "personal_account_title": "Primary personal account", "bank_app_title": "Bank", "invoices_app_title": "Invoices", "new_loan_title": "New loan", "new_loan_description": "Below, please enter the amount you want to loan. Everything will be automatically calculated based on your input.", "loan_type_label": "Loan type", "loan_amount_label": "Loan amount", "loan_payments_label": "Payments", "interest_rate_title": "Interest rate", "loan_duration_title": "Payments", "loan_each_payment_title": "Each payment", "interest_rate_amount_title": "Interest rate amount", "loan_total_amount_with_interest_title": "Total amount with interest", "day_single": "day", "day_plural": "days", "week_single": "week", "week_plural": "weeks", "request_modal_confirm_button": "Request Loan", "loan_select_type_placeholder": "Select loan type", "loans": "Loans", "request_new_loan": "Request new loan", "loan_amount_must_be_a_number": "Loan amount must be a number", "loan_amount_is_required": "Loan amount is required", "loan_amount_is_too_low": "Loan amount is too low", "loan_amount_is_too_high": "Loan amount is too high", "loan_duration_is_required": "Loan duration is required", "loan_duration_must_be_a_number": "Loan duration must be a number", "loan_duration_is_too_low": "Loan duration is too low", "loan_duration_is_too_high": "Loan duration is too high", "loan_request_failed_please_try_again": "Loan request failed, please try again.", "manage_loans": "Manage loans", "no_loan_requests": "No loan requests", "management_requests_tab": "Requests", "management_active_tab": "Active", "management_paid_off_tab": "Paid off", "management_other_tab": "Other", "approve_loan": "Approve", "reject_loan": "Reject", "requested_loan_overview_title": "Requested loan overview", "loan_type": "Loan type", "loan_player_identifier": "Citizen ID", "loan_full_name": "Full name", "loan_player_credit_score": "Credit score", "loan_player_unpaid_loans": "Unpaid loans", "loan_player_paid_loans": "Paid loans", "loan_player_cancelled_loans": "Cancelled loans", "loan_approve_confirmation_title": "Approve loan", "confirm": "Confirm", "loan_approve_confirmation": "Are you sure you want to approve this loan?", "approval_confirmation_title": "Loan approval confirmation", "reject_confirmation_title": "Reject loan", "loan_reject_confirmation": "Are you sure you want to reject this loan?", "view": "View", "loan_paid_off": "Paid off", "no_loan_paid_off": "No paid off loans", "loans_pending_player_approval": "Pending player approval", "loans_pending_manager_approval": "Pending manager approval", "loans_active": "Active", "loans_cancelled": "Cancelled", "loans_rejected": "Rejected", "loans_unknown": "Unknown", "no_active_loans": "No active loans", "no_loan_other_loans": "No other loans", "pay": "Pay", "active": "Active", "paid_off": "Paid off", "pending_request": "Pending request", "other": "Other", "no_payments_yet": "No payments yet", "loan_already_paid_amount": "Paid", "loan_overview": "Loan overview", "payment_was_latest": "Payment was missed, but paid.", "payment_was_on_time": "Payment was on time.", "cancellation_confirmation_title": "Loan cancellation confirmation", "loan_cancellation_confirmation": "Are you sure you want to cancel this loan?", "cancel_loan": "Cancel loan"}, "not_provided": "Not provided", "bank_legion_blip": "Bank", "checking_documents": "Checking documents...", "giving_back_documents": "Giving back documents...", "checking_card": "Checking card...", "giving_back_card": "Giving back card...", "open_bank_target": "Open Bank", "open_bank_point": "Open Bank", "bank_hawick_blip": "Bank", "bank_del_perro_blip": "Bank", "bank_route_68_blip": "Bank", "bank_pacific_blip": "Bank", "bank_paleto_blip": "Bank", "open_atm_target": "Open ATM", "open_atm_point": "Open ATM", "system": "System", "issue_personal_invoice_title": "Issue personal invoice", "issue_personal_invoice_description": "This will be a personal invoice, and funds will go directly to your personal account.", "issue_society_invoice_title": "Issue society invoice", "issue_society_invoice_description": "This will be a society invoice, and funds will go directly society account.", "unpaid_society_invoice_title": "View unpaid society invoices", "unpaid_society_invoice_description": "Here, you can view all unpaid society invoices.", "paid_society_invoice_title": "View paid society invoices", "paid_society_invoice_description": "Here, you can view all paid society invoices.", "billing_menu": "Billing menu", "issue_society_lookup_title": "Lookup citizen", "issue_society_lookup_description": "You're able to see how much unpaid invoices citizen has.", "issue_invoice_dialog_title": "Issue invoice", "player_id_title": "Player ID", "invoice_amount": "Invoice amount", "invoice_description": "Invoice reason", "invalid_amount": "Invalid amount", "invalid_player_id": "Invalid player ID", "invalid_description": "Invalid invoice reason", "player_not_online": "Player is not online", "player_is_too_far": "Player is too far", "no_personal_account": "Personal account not found", "invoice_issued": "Invoice issued successfully! ID: #%s", "not_in_society": "You're not in society", "society_invoices_disabled": "Society invoices are disabled", "no_business_account": "Business account not found", "payment_for_invoice": "Payment for invoice #%s, description: %s", "invoice_payed": "Invoice issued by you was paid! ID #%s, description: %s", "invoice_declined": "Invoice issued by you was declined! ID #%s, description: %s", "cancel_issued_invoice_title": "Cancel invoice", "cancel_issued_invoice_description": "Cancel issued invoice, please note that only person who issued invoice can cancel it.", "lookup_issued_invoice_title": "Lookup invoice", "lookup_issued_invoice_description": "You can lookup invoice information", "lookup_citizen_dialog_title": "Lookup player information", "lookup_citizen_show_title": "Information about citizen", "identifier": "Identifier", "unpaid_invoices_count": "Total unpaid invoices", "unpaid_invoices_sum": "Total unpaid invoices amount", "lookup_invoice_dialog_title": "Lookup invoice information", "invoice_id_title": "Invoice ID", "invalid_invoice_id": "Invalid Invoice ID", "lookup_invoice_show_title": "Invoice lookup information", "recipient": "Recipient", "invoice_id": "Invoice ID", "invoice_issued_by": "Invoice Issuer", "invoice_paid_status": "Paid", "invoice_declined_status": "Declined", "invoice_force_paid_status": "Force Paid", "invoice_cancelled_status": "Cancelled", "invoice_unpaid_status": "Unpaid", "invoice_cancelled": "Invoice cancelled successfully.", "overdue_invoices_processed": "Overdue invoices (%s) processed successfully.", "account_not_found": "Account not found", "account_frozen": "Account is frozen", "account_frozen_successfully": "Account frozen successfully.", "account_not_frozen": "Account is not frozen", "account_unfrozen_successfully": "Account unfrozen successfully.", "you_dont_have_permission_to_use_this_command": "You don't have permission to use this command.", "player_already_tracked": "Player is already being tracked", "player_is_not_being_tracked": "Player is not being tracked", "player_is_being_tracked": "Now Player (%s) is being tracked", "player_tracking_removed": "Player (%s) tracking removed", "no_invoices_to_pay": "No invoices to pay", "not_enough_money": "You don't have enough money", "invoices_payed": "Invoices payed successfully. Total amount: %s", "givecash_help_tip": "Gives cash from your pockets to provided ID.", "givecash_help_param": "Target players server ID.", "givecash_help_param2": "Amount you wish to give.", "givecash_no_user": "Invalid target player.", "givecash_no_amount": "You must enter an amount to give.", "givecash_no_zero": "You must enter an amount above 0.", "givecash_no_nearby": "There are no nearby players.", "givecash_give_money": "You gave $%s.", "givecash_receive_money": "You received $%s.", "invoices_recipient": "Recipient", "invoices_amount": "Amount", "invoices_issued_by": "Issued by", "invoices_status": "Status", "unpaid_invoices_title": "Unpaid invoices", "paid_invoices_title": "Paid invoices", "invoice_created_at": "Created at", "invoice_due_on": "Due on"}