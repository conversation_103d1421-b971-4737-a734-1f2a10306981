{"name": "banking", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.12", "@heroicons/vue": "^2.0.16", "@tailwindcss/forms": "^0.5.3", "@types/uuid": "^9.0.1", "@vee-validate/zod": "^4.8.4", "@vueform/slider": "^2.1.7", "dayjs": "^1.11.5", "defu": "^6.1.2", "luxon": "^3.3.0", "mitt": "^3.0.0", "pinia": "^2.0.32", "tippy.js": "^6.3.7", "uuid": "^9.0.0", "v3-infinite-loading": "^1.2.2", "vee-validate": "^4.8.4", "vue": "^3.2.47", "vue-final-modal": "^4.0.11", "vue-tailwind-datepicker": "^1.4.3", "zod": "^3.21.4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@types/luxon": "^3.2.0", "@types/node": "^18.15.3", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.14", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "prettier": "^2.8.4", "sass": "^1.62.1", "tailwindcss": "^3.2.7", "typescript": "~4.8.4", "vite": "^4.1.4", "vue-tsc": "^1.2.0"}}