import B from"./en-4402d6fc-aaf0afdb.js";var F=60,q=F*60,E=q*24,G=E*7,H=1e3,A=F*H,z=q*H,R=E*H,X=G*H,N="millisecond",O="second",b="minute",k="hour",M="day",L="week",v="month",J="quarter",m="year",Y="date",K="YYYY-MM-DDTHH:mm:ssZ",V="Invalid Date",_=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,tt=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,U=function(i,t,e){var n=String(i);return!n||n.length>=t?i:""+Array(t+1-n.length).join(e)+i},et=function(i){var t=-i.utcOffset(),e=Math.abs(t),n=Math.floor(e/60),r=e%60;return(t<=0?"+":"-")+U(n,2,"0")+":"+U(r,2,"0")},nt=function i(t,e){if(t.date()<e.date())return-i(e,t);var n=(e.year()-t.year())*12+(e.month()-t.month()),r=t.clone().add(n,v),s=e-r<0,a=t.clone().add(n+(s?-1:1),v);return+(-(n+(e-r)/(s?r-a:a-r))||0)},rt=function(i){return i<0?Math.ceil(i)||0:Math.floor(i)},it=function(i){var t={M:v,y:m,w:L,d:M,D:Y,h:k,m:b,s:O,ms:N,Q:J};return t[i]||String(i||"").toLowerCase().replace(/s$/,"")},st=function(i){return i===void 0};const ut={s:U,z:et,m:nt,a:rt,p:it,u:st};var x="en",w={};w[x]=B;var P="$isDayjsObject",Z=function(i){return i instanceof I||!!(i&&i[P])},W=function i(t,e,n){var r;if(!t)return x;if(typeof t=="string"){var s=t.toLowerCase();w[s]&&(r=s),e&&(w[s]=e,r=s);var a=t.split("-");if(!r&&a.length>1)return i(a[0])}else{var c=t.name;w[c]=t,r=c}return!n&&r&&(x=r),r||!n&&x},$=function(i,t){if(Z(i))return i.clone();var e=typeof t=="object"?t:{};return e.date=i,e.args=arguments,new I(e)},at=function(i,t){return $(i,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})},u=ut;u.l=W;u.i=Z;u.w=at;var ct=function(i){var t=i.date,e=i.utc;if(t===null)return new Date(NaN);if(u.u(t))return new Date;if(t instanceof Date)return new Date(t);if(typeof t=="string"&&!/Z$/i.test(t)){var n=t.match(_);if(n){var r=n[2]-1||0,s=(n[7]||"0").substring(0,3);return e?new Date(Date.UTC(n[1],r,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)):new Date(n[1],r,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)}}return new Date(t)},I=function(){function i(e){this.$L=W(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[P]=!0}var t=i.prototype;return t.parse=function(e){this.$d=ct(e),this.init()},t.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},t.$utils=function(){return u},t.isValid=function(){return this.$d.toString()!==V},t.isSame=function(e,n){var r=$(e);return this.startOf(n)<=r&&r<=this.endOf(n)},t.isAfter=function(e,n){return $(e)<this.startOf(n)},t.isBefore=function(e,n){return this.endOf(n)<$(e)},t.$g=function(e,n,r){return u.u(e)?this[n]:this.set(r,e)},t.unix=function(){return Math.floor(this.valueOf()/1e3)},t.valueOf=function(){return this.$d.getTime()},t.startOf=function(e,n){var r=this,s=u.u(n)?!0:n,a=u.p(e),c=function(g,p){var d=u.w(r.$u?Date.UTC(r.$y,p,g):new Date(r.$y,p,g),r);return s?d:d.endOf(M)},h=function(g,p){var d=[0,0,0,0],S=[23,59,59,999];return u.w(r.toDate()[g].apply(r.toDate("s"),(s?d:S).slice(p)),r)},o=this.$W,l=this.$M,f=this.$D,y="set"+(this.$u?"UTC":"");switch(a){case m:return s?c(1,0):c(31,11);case v:return s?c(1,l):c(0,l+1);case L:{var D=this.$locale().weekStart||0,T=(o<D?o+7:o)-D;return c(s?f-T:f+(6-T),l)}case M:case Y:return h(y+"Hours",0);case k:return h(y+"Minutes",1);case b:return h(y+"Seconds",2);case O:return h(y+"Milliseconds",3);default:return this.clone()}},t.endOf=function(e){return this.startOf(e,!1)},t.$set=function(e,n){var r,s=u.p(e),a="set"+(this.$u?"UTC":""),c=(r={},r[M]=a+"Date",r[Y]=a+"Date",r[v]=a+"Month",r[m]=a+"FullYear",r[k]=a+"Hours",r[b]=a+"Minutes",r[O]=a+"Seconds",r[N]=a+"Milliseconds",r)[s],h=s===M?this.$D+(n-this.$W):n;if(s===v||s===m){var o=this.clone().set(Y,1);o.$d[c](h),o.init(),this.$d=o.set(Y,Math.min(this.$D,o.daysInMonth())).$d}else c&&this.$d[c](h);return this.init(),this},t.set=function(e,n){return this.clone().$set(e,n)},t.get=function(e){return this[u.p(e)]()},t.add=function(e,n){var r=this,s;e=Number(e);var a=u.p(n),c=function(l){var f=$(r);return u.w(f.date(f.date()+Math.round(l*e)),r)};if(a===v)return this.set(v,this.$M+e);if(a===m)return this.set(m,this.$y+e);if(a===M)return c(1);if(a===L)return c(7);var h=(s={},s[b]=A,s[k]=z,s[O]=H,s)[a]||1,o=this.$d.getTime()+e*h;return u.w(o,this)},t.subtract=function(e,n){return this.add(e*-1,n)},t.format=function(e){var n=this,r=this.$locale();if(!this.isValid())return r.invalidDate||V;var s=e||K,a=u.z(this),c=this.$H,h=this.$m,o=this.$M,l=r.weekdays,f=r.months,y=r.meridiem,D=function(d,S,j,C){return d&&(d[S]||d(n,s))||j[S].slice(0,C)},T=function(d){return u.s(c%12||12,d,"0")},g=y||function(d,S,j){var C=d<12?"AM":"PM";return j?C.toLowerCase():C},p=function(d){switch(d){case"YY":return String(n.$y).slice(-2);case"YYYY":return u.s(n.$y,4,"0");case"M":return o+1;case"MM":return u.s(o+1,2,"0");case"MMM":return D(r.monthsShort,o,f,3);case"MMMM":return D(f,o);case"D":return n.$D;case"DD":return u.s(n.$D,2,"0");case"d":return String(n.$W);case"dd":return D(r.weekdaysMin,n.$W,l,2);case"ddd":return D(r.weekdaysShort,n.$W,l,3);case"dddd":return l[n.$W];case"H":return String(c);case"HH":return u.s(c,2,"0");case"h":return T(1);case"hh":return T(2);case"a":return g(c,h,!0);case"A":return g(c,h,!1);case"m":return String(h);case"mm":return u.s(h,2,"0");case"s":return String(n.$s);case"ss":return u.s(n.$s,2,"0");case"SSS":return u.s(n.$ms,3,"0");case"Z":return a}return null};return s.replace(tt,function(d,S){return S||p(d)||a.replace(":","")})},t.utcOffset=function(){return-Math.round(this.$d.getTimezoneOffset()/15)*15},t.diff=function(e,n,r){var s=this,a=u.p(n),c=$(e),h=(c.utcOffset()-this.utcOffset())*A,o=this-c,l=function(){return u.m(s,c)},f;switch(a){case m:f=l()/12;break;case v:f=l();break;case J:f=l()/3;break;case L:f=(o-h)/X;break;case M:f=(o-h)/R;break;case k:f=o/z;break;case b:f=o/A;break;case O:f=o/H;break;default:f=o;break}return r?f:u.a(f)},t.daysInMonth=function(){return this.endOf(v).$D},t.$locale=function(){return w[this.$L]},t.locale=function(e,n){if(!e)return this.$L;var r=this.clone(),s=W(e,n,!0);return s&&(r.$L=s),r},t.clone=function(){return u.w(this.$d,this)},t.toDate=function(){return new Date(this.valueOf())},t.toJSON=function(){return this.isValid()?this.toISOString():null},t.toISOString=function(){return this.$d.toISOString()},t.toString=function(){return this.$d.toUTCString()},i}(),Q=I.prototype;$.prototype=Q;[["$ms",N],["$s",O],["$m",b],["$H",k],["$W",M],["$M",v],["$y",m],["$D",Y]].forEach(function(i){Q[i[1]]=function(t){return this.$g(t,i[0],i[1])}});$.extend=function(i,t){return i.$i||(i(t,I,$),i.$i=!0),$};$.locale=W;$.isDayjs=Z;$.unix=function(i){return $(i*1e3)};$.en=w[x];$.Ls=w;$.p={};export{$};
