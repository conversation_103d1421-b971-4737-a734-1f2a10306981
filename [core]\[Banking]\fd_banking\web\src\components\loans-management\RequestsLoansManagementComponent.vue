<script setup lang="ts">
import { useLoansManagementStore } from '@/stores/loans-management'
import { useLocaleStore } from '@/stores/locale'
// @ts-ignore
import InfiniteLoading from 'v3-infinite-loading'
import 'v3-infinite-loading/lib/style.css'

import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { ChevronDownIcon } from '@heroicons/vue/20/solid'
import { useModal } from 'vue-final-modal'
import LoanViewRequestedModal from '../modals/LoanViewRequestedModal.vue'
import LoanApproveConfirmationModal from '../modals/LoanApproveConfirmationModal.vue'
import LoanRejectConfirmationModal from '../modals/LoanRejectConfirmationModal.vue'

const locale = useLocaleStore()
const loansManagement = useLoansManagementStore()

const {
  open: openRequestViewModal,
  close: closeRequestView,
  patchOptions: patchRequestViewOptions
} = useModal({
  component: LoanViewRequestedModal,
  attrs: {
    onCloseModal() {
      closeRequestView()
    }
  }
})

const openRequestView = (id: any) => {
  patchRequestViewOptions({
    attrs: {
      id
    }
  })

  openRequestViewModal()
}

const {
  open: openApproveLoanModal,
  close: closeApproveLoan,
  patchOptions: patchApproveLoanOptions
} = useModal({
  component: LoanApproveConfirmationModal,
  attrs: {
    onCloseModal() {
      closeApproveLoan()
    },
    onConfirm(id: any) {
      closeApproveLoan()
      loansManagement.approveLoan(id)
    }
  }
})

const openApproveLoan = (id: any) => {
  patchApproveLoanOptions({
    attrs: {
      id
    }
  })

  openApproveLoanModal()
}

const {
  open: openRejectLoanModal,
  close: closeRejectLoan,
  patchOptions: patchRejectLoanOptions
} = useModal({
  component: LoanRejectConfirmationModal,
  attrs: {
    onCloseModal() {
      closeRejectLoan()
    },
    onConfirm(id: any) {
      closeRejectLoan()
      loansManagement.rejectLoan(id)
    }
  }
})

const openRejectLoan = (id: any) => {
  patchRejectLoanOptions({
    attrs: {
      id
    }
  })

  openRejectLoanModal()
}
</script>
<template>
  <div class="">
    <div
      class="flex flex-col flex-1 justify-center items-center py-10 gap-5"
      v-if="loansManagement.loans.length < 1"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        data-name="Layer 1"
        width="647.63626"
        height="632.17383"
        viewBox="0 0 647.63626 632.17383"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        class="h-60"
      >
        <path
          d="M687.3279,276.08691H512.81813a15.01828,15.01828,0,0,0-15,15v387.85l-2,.61005-42.81006,13.11a8.00676,8.00676,0,0,1-9.98974-5.31L315.678,271.39691a8.00313,8.00313,0,0,1,5.31006-9.99l65.97022-20.2,191.25-58.54,65.96972-20.2a7.98927,7.98927,0,0,1,9.99024,5.3l32.5498,106.32Z"
          transform="translate(-276.18187 -133.91309)"
          fill="#f2f2f2"
        />
        <path
          d="M725.408,274.08691l-39.23-128.14a16.99368,16.99368,0,0,0-21.23-11.28l-92.75,28.39L380.95827,221.60693l-92.75,28.4a17.0152,17.0152,0,0,0-11.28028,21.23l134.08008,437.93a17.02661,17.02661,0,0,0,16.26026,12.03,16.78926,16.78926,0,0,0,4.96972-.75l63.58008-19.46,2-.62v-2.09l-2,.61-64.16992,19.65a15.01489,15.01489,0,0,1-18.73-9.95l-134.06983-437.94a14.97935,14.97935,0,0,1,9.94971-18.73l92.75-28.4,191.24024-58.54,92.75-28.4a15.15551,15.15551,0,0,1,4.40966-.66,15.01461,15.01461,0,0,1,14.32032,10.61l39.0498,127.56.62012,2h2.08008Z"
          transform="translate(-276.18187 -133.91309)"
          fill="#3f3d56"
        />
        <path
          d="M398.86279,261.73389a9.0157,9.0157,0,0,1-8.61133-6.3667l-12.88037-42.07178a8.99884,8.99884,0,0,1,5.9712-11.24023l175.939-53.86377a9.00867,9.00867,0,0,1,11.24072,5.9707l12.88037,42.07227a9.01029,9.01029,0,0,1-5.9707,11.24072L401.49219,261.33887A8.976,8.976,0,0,1,398.86279,261.73389Z"
          transform="translate(-276.18187 -133.91309)"
          fill="#d1d5db"
        />
        <circle cx="190.15351" cy="24.95465" r="20" fill="#d1d5db" />
        <circle cx="190.15351" cy="24.95465" r="12.66462" fill="#fff" />
        <path
          d="M878.81836,716.08691h-338a8.50981,8.50981,0,0,1-8.5-8.5v-405a8.50951,8.50951,0,0,1,8.5-8.5h338a8.50982,8.50982,0,0,1,8.5,8.5v405A8.51013,8.51013,0,0,1,878.81836,716.08691Z"
          transform="translate(-276.18187 -133.91309)"
          fill="#e6e6e6"
        />
        <path
          d="M723.31813,274.08691h-210.5a17.02411,17.02411,0,0,0-17,17v407.8l2-.61v-407.19a15.01828,15.01828,0,0,1,15-15H723.93825Zm183.5,0h-394a17.02411,17.02411,0,0,0-17,17v458a17.0241,17.0241,0,0,0,17,17h394a17.0241,17.0241,0,0,0,17-17v-458A17.02411,17.02411,0,0,0,906.81813,274.08691Zm15,475a15.01828,15.01828,0,0,1-15,15h-394a15.01828,15.01828,0,0,1-15-15v-458a15.01828,15.01828,0,0,1,15-15h394a15.01828,15.01828,0,0,1,15,15Z"
          transform="translate(-276.18187 -133.91309)"
          fill="#3f3d56"
        />
        <path
          d="M801.81836,318.08691h-184a9.01015,9.01015,0,0,1-9-9v-44a9.01016,9.01016,0,0,1,9-9h184a9.01016,9.01016,0,0,1,9,9v44A9.01015,9.01015,0,0,1,801.81836,318.08691Z"
          transform="translate(-276.18187 -133.91309)"
          fill="#d1d5db"
        />
        <circle cx="433.63626" cy="105.17383" r="20" fill="#d1d5db" />
        <circle cx="433.63626" cy="105.17383" r="12.18187" fill="#fff" />
      </svg>
      <span class="text-2xl font-semibold text-gray-300">{{ locale.get('no_loan_requests') }}</span>
    </div>
    <table class="w-full whitespace-nowrap" v-else>
      <tbody>
        <tr
          tabindex="0"
          v-for="loan in loansManagement.loans"
          :key="loan.id"
          class="focus:outline-none h-16 border border-[#373A40] rounded-2xl mb-3"
        >
          <td class="">
            <div class="flex items-center pl-5">
              <p class="text-base font-medium leading-none text-gray-300 mr-2">{{ loan.type }}</p>
            </div>
          </td>
          <td class="pl-5">
            <div class="flex items-center pl-5">
              <p class="text-base font-medium leading-none text-gray-300 mr-2">
                {{ loan.identifier }}
              </p>
            </div>
          </td>
          <td class="pl-24">
            <div class="flex items-center">
              <i class="text-sm fa-solid fa-user text-gray-300"></i>
              <p class="text-sm leading-none text-gray-300 ml-2">{{ loan.given_by_label }}</p>
            </div>
          </td>
          <td class="pl-5">
            <div class="flex items-center">
              <i class="text-sm fa-solid fa-money-bill-1 text-gray-300"></i>
              <p class="text-sm leading-none text-gray-300 ml-2">
                {{
                  loan.amount.toLocaleString(locale.get('currency_language'), {
                    style: 'currency',
                    currency: locale.get('currency')
                  })
                }}
              </p>
            </div>
          </td>
          <td class="pl-5">
            <div class="flex items-center">
              <i class="text-sm fa-solid fa-money-bills text-gray-300"></i>
              <p class="text-sm leading-none text-gray-300 ml-2">
                {{
                  loan.total.toLocaleString(locale.get('currency_language'), {
                    style: 'currency',
                    currency: locale.get('currency')
                  })
                }}
              </p>
            </div>
          </td>
          <td class="pl-5">
            <div class="flex items-center">
              <i class="text-sm fa-solid fa-percent text-gray-300"></i>
              <p class="text-sm leading-none text-gray-300 ml-2">{{ loan.interest }}%</p>
            </div>
          </td>

          <td class="pl-4">
            <div class="inline-flex rounded-md shadow-sm">
              <button
                type="button"
                @click.prevent="openRequestView(loan.id)"
                class="relative inline-flex items-center rounded-l-md bg-transparent px-3 py-2 text-sm font-semibold text-gray-400 ring-1 ring-inset ring-[#373A40] hover:bg-[#373A40] hover:text-white focus:z-10"
              >
                {{ locale.get('view') }}
              </button>
              <Menu as="div" class="relative -ml-px block">
                <MenuButton
                  class="relative inline-flex items-center rounded-r-md bg-transparent px-2 py-2 text-gray-400 ring-1 ring-inset ring-[#373A40] hover:bg-[#373A40] hover:text-white focus:z-10"
                >
                  <ChevronDownIcon class="h-5 w-5" />
                </MenuButton>
                <transition
                  enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0 scale-95"
                  enter-to-class="transform opacity-100 scale-100"
                  leave-active-class="transition ease-in duration-75"
                  leave-from-class="transform opacity-100 scale-100"
                  leave-to-class="transform opacity-0 scale-95"
                >
                  <MenuItems
                    class="absolute right-0 z-10 -mr-1 mt-2 w-56 origin-top-right rounded-md bg-[#25262b] shadow-lg ring-1 ring-[#373A40] focus:outline-none"
                  >
                    <div class="py-1">
                      <MenuItem v-slot="{ active }">
                        <a
                          @click.prevent="openApproveLoan(loan.id)"
                          :class="[
                            active ? 'bg-[#85b899] text-white' : 'text-gray-400',
                            'block px-4 py-2 text-sm'
                          ]"
                          >{{ locale.get('approve_loan') }}</a
                        >
                      </MenuItem>
                      <MenuItem v-slot="{ active }">
                        <a
                          @click.prevent="openRejectLoan(loan.id)"
                          :class="[
                            active ? 'bg-[#85b899] text-white' : 'text-gray-400',
                            'block px-4 py-2 text-sm'
                          ]"
                          >{{ locale.get('reject_loan') }}</a
                        >
                      </MenuItem>
                    </div>
                  </MenuItems>
                </transition>
              </Menu>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <InfiniteLoading
      class="flex items-center justify-center text-gray-500"
      @infinite="loansManagement.loansLoading"
      :identifier="loansManagement.identifier"
      :slots="{
        // eslint-disable-next-line no-irregular-whitespace
        complete: ' ',
        error: locale.get('error_occurred')
      }"
    />
  </div>
</template>
